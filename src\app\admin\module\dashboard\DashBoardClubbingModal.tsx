import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from 'react';
import {toast} from 'react-toastify';
import axios from 'axios';
import {useAuth} from "../../../modules/auth";
import {
    clubbingEmployeeDetailsData,
    DasboardClubbingInsertParams,
    DasboardClubEmployeeListParams,
    employee_details,
    TripDetailData
} from "../../../modules/auth/core/dashboard/_dashboard_models";
import {Modal} from 'bootstrap';
import {
    getClubbingEmployeeDetailsAction,
    getClubbingEmployeeListAction,
    insertPassangerClubbingAction
} from '../../../modules/auth/core/dashboard/_dashboard_requests';
import ReactSelect from "../../../Helper/ReactSelect";
import {AgGridReact} from 'ag-grid-react';
import {ICellRendererParams, IDatasource} from 'ag-grid-community';
import Select from 'react-select';

interface Option {
    value: string;
    label: string;
}

interface mobileLists {
    EMPLOYEES_ID: string;
    EMPNAME: string;
}

interface locationLists {
    LOCATION_ID: string;
    LOCATION_NAME: string;
}

export interface employeeDetails {
    EMPLOYEES_ID: string
    EMPNAME: string;
    GENDER: string;
    EMAIL: string;
    ADDRESS: string;
    LATITUDE: string;
    LOCATION_ID: number;
    LOCATION_NAME: string;
    LONGITUDE: string;
    MOBILE: string;
    MOBILE_CATEGORY: string;
    MOBILE_GCM: string;
    PROJECT_NAME: string;
}

interface ClubbingModalProps {
    id: string;
    title: string;
    TripDetailData: TripDetailData | null;
    setAgGridRefresh: React.Dispatch<React.SetStateAction<boolean>>;
    setIsClubbingModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface ClubbingModalRef {
    openModal: () => void;
    closeModal: () => void;
}

const DashBoardClubbingModal = forwardRef<ClubbingModalRef, ClubbingModalProps>(({
                                                                                     id,
                                                                                     title,
                                                                                     TripDetailData,
                                                                                     setAgGridRefresh,
                                                                                     setIsClubbingModalOpen
                                                                                 }, ref) => {
    const modalRef = useRef<HTMLDivElement>(null);
    const ClubbGridRef = useRef<AgGridReact>(null);
    const datasourceRef = useRef<IDatasource | null>(null);


    const [loading, setLoading] = useState<boolean>(false);
    const [employeeDetailsLoading, setEmployeeDetailsLoading] = useState<boolean>(false);
    const [clubbingActionLoading, setClubbingActionLoading] = useState<boolean>(false);


    const [error, setError] = useState<string | null>(null);
    const [networkType, setNetworkType] = useState<string | null>(null);


    const [mobile_lists, setMobile_lists] = useState<Option[]>([]);
    const [location_Lists, setLocation_Lists] = useState<Option[]>([]);
    const [employees, setEmployees] = useState<employee_details[]>([]);
    const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>('');

    const {logout} = useAuth();
    const rosterId = TripDetailData?.ROSTER_ID ?? 0;

    useEffect(() => {
        const modalElement = document.getElementById(id);

        const handleClubbingModalHidden = (e: Event) => {
            e.stopPropagation();
            setIsClubbingModalOpen(false);
        };

        if (modalElement) {
            modalElement.addEventListener('hidden.bs.modal', handleClubbingModalHidden);
        }

        return () => {
            if (modalElement) {
                modalElement.removeEventListener('hidden.bs.modal', handleClubbingModalHidden);
            }
        };
    }, []);

    useEffect(() => {
        const modalElement = modalRef.current;
        if (modalElement) {
            new Modal(modalElement);
        }
    }, []);

    const fetchClubEmployeesData = useCallback(async () => {
        try {
            setLoading(true);
            setNetworkType('fetchClubEmployeesData');

            const clubEmployeeListParams: DasboardClubEmployeeListParams = {
                roster_id: rosterId,
            }

            const response = await getClubbingEmployeeListAction(clubEmployeeListParams);

            setMobile_lists(response.data.employees.map((mobileList: mobileLists) => ({
                value: mobileList.EMPLOYEES_ID.toString(),
                label: mobileList.EMPLOYEES_ID
            })));

            setError(null);
        } catch (err) {
            console.error('Error fetching club employees data:', err);
            let errorMessage = 'Failed to load employee data. Please try again.';
            if (axios.isAxiosError(err)) {
                const statusCode = err.response?.status;
                if (statusCode === 403 || statusCode === 401) {
                    logout();
                    closeModal();
                    errorMessage = 'Your session has expired. Please log in again.';
                } else if (statusCode === 500) {
                    errorMessage = 'An error occurred while fetching employee data. Please contact admin.';
                }
            }
            setError(errorMessage);
            toast.error(errorMessage);
        } finally {
            setLoading(false);
        }
    }, [logout, rosterId]);

    const fetchEmployeeDetails = async (empId: string) => {
        if (!empId || rosterId === 0) {
            toast.error('Please select a valid employee ID');
            return;
        }

        try {
            setEmployeeDetailsLoading(true);
            setNetworkType('fetchEmployeeDetails');

            const clubbingEmployeeDetails: clubbingEmployeeDetailsData = {
                emp_id: empId,
                login_datetime: TripDetailData?.TRIPINTIME ?? '',
                trip_type: TripDetailData?.TRIP_TYPE ?? '',
            };

            const response = await getClubbingEmployeeDetailsAction(clubbingEmployeeDetails);

            if (response.data.success) {
                setLocation_Lists(response.data.locations.map((location_List: locationLists) => ({
                    value: location_List.LOCATION_ID.toString(),
                    label: location_List.LOCATION_NAME
                })));

                setEmployees(response.data.employees);
                setError(null);
            } else {
                const errorMessage = response.data.message || 'Failed to fetch employee details';
                setError(errorMessage);
                toast.error(errorMessage);
            }
        } catch (err) {
            console.error('Error fetching employee details:', err);
            let errorMessage = 'Failed to fetch employee details. Please try again.';
            if (axios.isAxiosError(err)) {
                const statusCode = err.response?.status;
                if (statusCode === 403 || statusCode === 401) {
                    logout();
                    closeModal();
                    errorMessage = 'Your session has expired. Please log in again.';
                } else if (statusCode === 500) {
                    errorMessage = 'An error occurred while fetching employee details. Please contact admin.';
                }
            }
            setError(errorMessage);
            toast.error(errorMessage);
        } finally {
            setEmployeeDetailsLoading(false);
        }
    };

    const handleTryingAgainClick = () => {
        setError(null);

        switch (networkType) {
            case 'fetchClubEmployeesData':
                fetchClubEmployeesData();
                break;
            case 'fetchEmployeeDetails':
                if (selectedEmployeeId) {
                    fetchEmployeeDetails(selectedEmployeeId);
                }
                break;
            default:
                console.error('Unknown network type');
        }
    };

    const handleEmployeeIdChange = (option: Option | null) => {
        setSelectedEmployeeId(option?.value || '');

        setEmployees([]);
        setLocation_Lists([]);
    };

    const handleSubmit = () => {
        if (selectedEmployeeId) {
            fetchEmployeeDetails(selectedEmployeeId);
        } else {
            toast.error('Please select an employee ID first');
        }
    };

    useEffect(() => {
        fetchClubEmployeesData();
    }, [fetchClubEmployeesData]);

    const openModal = () => {
        const modalElement = document.getElementById(id);
        if (modalElement instanceof HTMLElement) {
            const modalInstance = Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.show();
            } else {
                new Modal(modalElement).show();
            }
        }
    };

    const closeModal = () => {
        const modalElement = document.getElementById(id);
        if (modalElement instanceof HTMLElement) {
            const modalInstance = Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.hide();
                setIsClubbingModalOpen(false);
            }
        }
    };

    useImperativeHandle(ref, () => ({
        openModal,
        closeModal
    }));

    const selectValueCellRenderer: React.FC<ICellRendererParams> = (props) => {
        const [selectedLocation, setSelectedLocation] = useState<string>('');

        const onLocationChange = useCallback((selectedOption: { value: string; label: string } | null) => {
            if (selectedOption) {
                setSelectedLocation(selectedOption.value);
                if (props.node) {
                    props.node.setDataValue('location_id', selectedOption.value);
                }
            }
        }, []);

        return (
            <div style={{width: '100%'}}>
                <Select
                    options={location_Lists}
                    onChange={onLocationChange}
                    menuPlacement="bottom"
                    menuPosition="fixed"
                    className="react-select-container"
                    classNamePrefix="react-select"
                    menuPortalTarget={document.body}
                    styles={{
                        menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999
                        }),
                        control: (base) => ({
                            ...base,
                            minHeight: '38px',
                            height: '38px',
                            border: '1px solid #dee2e6',
                            boxShadow: 'none',
                            '&:hover': {
                                border: '1px solid #dee2e6'
                            }
                        }),
                        input: (base) => ({
                            ...base,
                            margin: '0',
                            padding: '0',
                        }),
                        valueContainer: (base) => ({
                            ...base,
                            padding: '0 8px',
                            height: '36px'
                        }),
                        dropdownIndicator: (base) => ({
                            ...base,
                            padding: '4px'
                        })
                    }}
                />
            </div>
        );
    };

    const datePickerCellRenderer: React.FC<ICellRendererParams> = (props) => {
        const [pickupDropTime, setPickupDropTime] = useState<string>(props.value || '');

        const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
            const newDate = event.target.value;
            setPickupDropTime(newDate);
            if (props.node) {
                props.node.setDataValue('pickdroptime', newDate);
            }
        };

        return (
            <div style={{width: '100%', padding: '0 1px'}}>
                <input
                    type="datetime-local"
                    style={{
                        height: '38px',
                        width: '100%',
                        padding: '0 8px',
                        border: '1px solid #dee2e6',
                        borderRadius: '0.25rem',
                        outline: 'none',
                        boxShadow: 'none'
                    }}
                    value={pickupDropTime}
                    onChange={handleDateChange}
                />
            </div>
        );
    };

    const clubbingActionCellRenderer: React.FC<ICellRendererParams> = (props) => {
        const [isSubmitting, setIsSubmitting] = useState(false);
        const [actionError, setActionError] = useState<string | null>(null);

        const onSubmitAction = useCallback(async () => {
            const pickdroptime = props.data.pickdroptime;
            if (!pickdroptime) {
                toast.error('Please select a pickdroptime before performing the action.');
                return;
            }

            const location_id = props.data.location_id;
            if (!location_id) {
                toast.error('Please select a Location before performing the action.');
                return;
            }


            const formatDateTimeForAPI = (datetimeLocal: string): string => {
                if (!datetimeLocal) return '';


                const dateTimeWithSeconds = datetimeLocal.includes(':') && datetimeLocal.split(':').length === 2
                    ? `${datetimeLocal}:00`
                    : datetimeLocal;


                return dateTimeWithSeconds.replace('T', ' ');
            };

            try {
                setIsSubmitting(true);
                setActionError(null);

                const formattedPickdroptime = formatDateTimeForAPI(pickdroptime);

                const dasboardClubbingInsertParams: DasboardClubbingInsertParams = {
                    empid: props.data.EMPLOYEES_ID,
                    location_id: location_id,
                    roster_id: rosterId,
                    mobile: props.data.MOBILE,
                    logindatetime: TripDetailData?.TRIPINTIME ?? '',
                    pickdroptime: formattedPickdroptime,
                    asset_movement: "test",
                };

                const response = await insertPassangerClubbingAction(dasboardClubbingInsertParams);

                if (response.data.success) {
                    toast.success('Employee clubbed successfully!');
                    ClubbGridRef?.current?.api?.refreshInfiniteCache();
                    setAgGridRefresh(true);
                    closeModal();
                } else {
                    const errorMessage = response.data.message || 'Failed to club employee';
                    setActionError(errorMessage);
                    toast.error(errorMessage);
                }
            } catch (err) {
                console.error('Error clubbing employee:', err);
                let errorMessage = 'Failed to club employee. Please try again.';
                if (axios.isAxiosError(err)) {
                    const statusCode = err.response?.status;
                    if (statusCode === 403 || statusCode === 401) {
                        logout();
                        closeModal();
                        errorMessage = 'Your session has expired. Please log in again.';
                    } else if (statusCode === 500) {
                        errorMessage = 'An error occurred while clubbing employee. Please contact admin.';
                    }
                }
                setActionError(errorMessage);
                toast.error(errorMessage);
            } finally {
                setIsSubmitting(false);
            }
        }, []);

        const handleRetry = () => {
            onSubmitAction();
        };

        return (
            <div className="d-flex align-items-center mt-2 gap-1">
                {!actionError ? (
                    <button
                        onClick={onSubmitAction}
                        disabled={isSubmitting}
                        type="button"
                        className={`btn btn-sm ${isSubmitting ? 'btn-success' : 'action-btn text-light'} p-1`}
                        title="Add Route"
                        style={{
                            width: isSubmitting ? 'auto' : '36px',
                            minWidth: isSubmitting ? '80px' : '36px',
                            whiteSpace: 'nowrap'
                        }}
                    >
                        {!isSubmitting && <i className="fas fa-user-plus text-success"></i>}
                        {isSubmitting && (
                            <span className="indicator-progress" style={{display: 'block'}}>
                                Adding...
                                <span className="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        )}
                    </button>
                ) : (
                    <button
                        onClick={handleRetry}
                        disabled={isSubmitting}
                        className="btn btn-danger btn-sm"
                        style={{fontSize: '12px'}}
                    >
                        {!isSubmitting && <span className="indicator-label">retry</span>}
                        {isSubmitting && (
                            <span className="indicator-progress" style={{display: 'block'}}>
                                Retrying...
                                <span className="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        )}
                    </button>
                )}
            </div>
        );
    };


    const columnDefs = useMemo(() => [
        {field: 'EMPNAME', headerName: 'Name'},
        {field: 'EMAIL', headerName: 'Email'},
        {field: 'GENDER', headerName: 'Gender'},
        {field: 'EMPLOYEES_ID', headerName: 'Employee Id'},
        {field: 'PROJECT_NAME', headerName: 'Project Name'},
        {field: 'ADDRESS', headerName: 'Address'},
        {
            headerName: 'Pickup Area',
            field: 'location_id',
            cellRenderer: selectValueCellRenderer,
            sortable: false,
            minWidth: 250,
            filter: false,
        },
        {
            headerName: 'Pickup/Drop Time',
            field: 'pickdroptime',
            cellRenderer: datePickerCellRenderer,
            minWidth: 230,
            sortable: false,
            filter: false,
        },
        {
            headerName: 'Action',
            cellRenderer: clubbingActionCellRenderer,
            sortable: false,
            filter: false,
        },
    ], [location_Lists]);


    const defaultColDef = useMemo(() => ({
        filter: 'agTextColumnFilter',
        filterParams: {
            filterOptions: ['contains'],
            maxNumConditions: 1
        },
        floatingFilter: true,
        sortable: true,
    }), []);

    if (error) {
        return (
            <div className="modal fade" tabIndex={-1} id={id} ref={modalRef}>
                <div className="modal-dialog modal-xl">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{title}</h5>
                            <button
                                type="button"
                                className="btn-close"
                                data-bs-dismiss="modal"
                                aria-label="Close"
                            ></button>
                        </div>
                        <div className="modal-body">
                            <div className="d-flex flex-column justify-content-center align-items-center">
                                <div className="text-center mb-4">
                                    <div className="alert alert-danger" role="alert">
                                        {error}
                                    </div>
                                </div>
                                <div className="d-flex gap-2">
                                    <button
                                        type="button"
                                        className="btn btn-light"
                                        onClick={closeModal}
                                        disabled={loading || employeeDetailsLoading}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        className="btn btn-primary"
                                        onClick={handleTryingAgainClick}
                                        disabled={loading || employeeDetailsLoading}
                                    >
                                        {(loading || employeeDetailsLoading) ? (
                                            <span className='indicator-progress' style={{display: 'block'}}>
                                                Please wait...
                                                <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                                            </span>
                                        ) : (
                                            <span className="indicator-label">Try Again</span>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="modal fade" tabIndex={-1} id={id} ref={modalRef}>
            <div className="modal-dialog modal-xl">
                <div className="modal-content">
                    {loading && (
                        <>
                            <div className="custom-overlay show"></div>
                            <div className="position-absolute top-50 start-50 translate-middle custom-loading-spinner">
                                <div className="d-flex flex-column align-items-center">
                                    <div className="spinner-border text-primary" role="status"/>
                                    <div className="mt-2 text-primary fw-bold">Loading...</div>
                                </div>
                            </div>
                        </>
                    )}

                    <div className="modal-header">
                        <h5 className="modal-title">{title}</h5>
                        <button
                            type="button"
                            className="btn-close"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                        ></button>
                    </div>

                    <div className="modal-body">
                        <div className="container mb-4">
                            <div className="row justify-content-center align-items-center py-2">
                                <div className="col-md-2">
                                    <label htmlFor="emp_id" className="form-label mb-0">
                                        Mobile Number:
                                    </label>
                                </div>
                                <div className="col-md-4">
                                    <ReactSelect
                                        options={mobile_lists}
                                        placeholder="Select Mobile Number"
                                        disabled={employeeDetailsLoading || loading}
                                        onChange={handleEmployeeIdChange}
                                        value={mobile_lists.find(option => option.value === selectedEmployeeId) || null}
                                    />
                                </div>
                                <div className="col-md-1">
                                    <button
                                        type="button"
                                        className="btn btn-primary"
                                        onClick={handleSubmit}
                                        disabled={employeeDetailsLoading || loading || !selectedEmployeeId}
                                    >
                                        {!employeeDetailsLoading && <span className="indicator-label">Go</span>}
                                        {employeeDetailsLoading && (
                                            <span className='indicator-progress' style={{display: 'block'}}>
                                                <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                                            </span>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>

                        {employees.length > 0 && (
                            <div className="ag-theme-quartz" style={{height: 300, width: '100%'}}>
                                <AgGridReact
                                    ref={ClubbGridRef}
                                    rowData={employees}
                                    columnDefs={columnDefs}
                                    defaultColDef={defaultColDef}
                                    pagination={true}
                                    paginationPageSize={20}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
});

export default DashBoardClubbingModal;